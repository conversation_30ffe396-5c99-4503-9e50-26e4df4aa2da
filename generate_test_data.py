#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد البيانات التجريبية - إضافة 500 نشاط تجريبي
"""

import sqlite3
import random
import json
from datetime import datetime, timedelta

def connect_database():
    """الاتصال بقاعدة البيانات"""
    conn = sqlite3.connect("activities.db")
    return conn

def generate_test_data():
    """توليد 500 من البيانات التجريبية"""
    
    # البيانات الأساسية
    persons = [
        "أحمد محمد علي", "فاطمة سالم أحمد", "محمد عبدالله حسن", "سارة علي محمد",
        "خالد حسن علي", "ليلى محمد سالم", "عبدالرحمن أحمد علي", "نادية حسن محمد",
        "يوسف علي أحمد", "زينب محمد حسن", "عمر سالم علي", "رقية أحمد محمد",
        "حسام علي حسن", "منى محمد سالم", "طارق حسن أحمد", "هدى علي محمد",
        "وليد محمد علي", "سمية حسن سالم", "ماجد أحمد علي", "إيمان محمد حسن"
    ]
    
    # التصنيفات والأنشطة
    categories_activities = {
        "التعليم والتدريب": {
            "subcategories": ["التدريب المهني", "التعليم المستمر", "محو الأمية", "التعليم التقني"],
            "activities": [
                "ورشة تدريبية في الحاسوب", "دورة تعليم اللغة الإنجليزية", "برنامج محو الأمية",
                "تدريب على المهارات المهنية", "ورشة تطوير المهارات", "دورة في إدارة الأعمال"
            ]
        },
        "الصحة والتوعية": {
            "subcategories": ["التوعية الصحية", "الصحة الإنجابية", "التغذية", "الوقاية من الأمراض"],
            "activities": [
                "حملة توعية صحية", "ورشة عن التغذية السليمة", "برنامج الصحة الإنجابية",
                "حملة تطعيم", "ندوة عن الوقاية من الأمراض", "فحص طبي مجاني"
            ]
        },
        "التنمية المجتمعية": {
            "subcategories": ["التنمية الاقتصادية", "التنمية الاجتماعية", "تمكين المرأة", "تنمية الشباب"],
            "activities": [
                "مشروع تنموي للمجتمع", "برنامج تمكين المرأة", "مشروع دعم الشباب",
                "ورشة ريادة الأعمال", "برنامج التنمية الاقتصادية", "مشروع التماسك الاجتماعي"
            ]
        },
        "البيئة والزراعة": {
            "subcategories": ["الزراعة المستدامة", "حماية البيئة", "إدارة المياه", "الطاقة المتجددة"],
            "activities": [
                "مشروع زراعي مستدام", "حملة تنظيف البيئة", "ورشة عن إدارة المياه",
                "مشروع الطاقة الشمسية", "برنامج التشجير", "ندوة عن حماية البيئة"
            ]
        },
        "الثقافة والفنون": {
            "subcategories": ["الفنون التراثية", "الأدب والشعر", "المسرح والسينما", "الموسيقى"],
            "activities": [
                "مهرجان ثقافي", "أمسية شعرية", "عرض مسرحي", "ورشة فنون تراثية",
                "معرض فني", "حفل موسيقي تراثي"
            ]
        }
    }
    
    # المحافظات والمناطق اليمنية
    locations = {
        "صنعاء": {
            "districts": ["الأمانة", "سنحان", "بني حشيش", "همدان", "أرحب"],
            "sub_districts": ["شعوب", "الثورة", "الصافية", "معين", "الوحدة"],
            "villages": ["الحصبة", "شملان", "بيت بوس", "الروضة", "السبعين"]
        },
        "عدن": {
            "districts": ["المعلا", "كريتر", "التواهي", "الشيخ عثمان", "دار سعد"],
            "sub_districts": ["المنصورة", "صيرة", "البريقة", "الممدارة", "الحسوة"],
            "villages": ["الشعب", "القلوعة", "الحوطة", "الكود", "المحطة"]
        },
        "تعز": {
            "districts": ["المدينة", "صالة", "المعافر", "الشمايتين", "الوازعية"],
            "sub_districts": ["القاهرة", "الدمنة", "الأهجر", "الحوبان", "الشعب"],
            "villages": ["الروضة", "الضباب", "الأشراف", "الحوك", "الدحي"]
        },
        "الحديدة": {
            "districts": ["الحديدة", "الحوك", "الدريهمي", "بيت الفقيه", "زبيد"],
            "sub_districts": ["الميناء", "الحالي", "الجراحي", "الزهرة", "الصالحية"],
            "villages": ["الخوخة", "الفازة", "الجبانة", "الحسينية", "الزيدية"]
        },
        "إب": {
            "districts": ["إب", "السبرة", "النادرة", "حبيش", "المخادر"],
            "sub_districts": ["الظهار", "الشعر", "الرضمة", "القفر", "الحدأ"],
            "villages": ["الضالع", "الحشاء", "المحويت", "الحجرية", "الشرجة"]
        }
    }
    
    # حالات النشاط والأولويات
    statuses = ["مخطط", "جاري", "مكتمل", "ملغي"]
    priorities = ["عالي", "متوسط", "منخفض"]
    approval_status = ["نعم", "لا"]
    
    # نتائج وتوصيات متنوعة
    results = [
        "تم تحقيق الأهداف المطلوبة بنجاح",
        "نجاح جزئي مع تحديات في التنفيذ",
        "تجاوز التوقعات المحددة",
        "نتائج مرضية مع إمكانية التحسين",
        "تحديات واجهت التنفيذ ولكن تم التغلب عليها",
        "نجاح كبير وتفاعل إيجابي من المستفيدين",
        "نتائج متوسطة تحتاج إلى متابعة",
        "تحقيق جزئي للأهداف المحددة"
    ]
    
    recommendations = [
        "زيادة الدعم المالي للمشاريع المماثلة",
        "تكرار النشاط في مناطق أخرى",
        "تطوير الأدوات والوسائل المستخدمة",
        "زيادة فترة التدريب والتأهيل",
        "تحسين آليات المتابعة والتقييم",
        "إشراك المجتمع المحلي بشكل أكبر",
        "توفير موارد إضافية للاستدامة",
        "تطوير شراكات مع منظمات أخرى"
    ]
    
    notes = [
        "تم التنفيذ وفقاً للخطة المحددة",
        "تأخير طفيف بسبب الظروف الجوية",
        "تفاعل إيجابي من المجتمع المحلي",
        "تحديات لوجستية تم التغلب عليها",
        "مشاركة فعالة من الفئة المستهدفة",
        "تنسيق جيد مع الجهات المحلية",
        "نتائج تفوق التوقعات المحددة",
        "توصيات لتطوير الأنشطة المستقبلية"
    ]
    
    # توليد البيانات
    activities_data = []
    
    for i in range(500):
        # اختيار تصنيف عشوائي
        parent_category = random.choice(list(categories_activities.keys()))
        sub_category = random.choice(categories_activities[parent_category]["subcategories"])
        activity_name = random.choice(categories_activities[parent_category]["activities"])
        
        # اختيار موقع عشوائي
        governorate = random.choice(list(locations.keys()))
        district = random.choice(locations[governorate]["districts"])
        sub_district = random.choice(locations[governorate]["sub_districts"])
        village = random.choice(locations[governorate]["villages"])
        
        # توليد تاريخ عشوائي (آخر سنتين)
        start_date = datetime.now() - timedelta(days=730)
        end_date = datetime.now() + timedelta(days=365)
        random_days = random.randint(0, (end_date - start_date).days)
        activity_date = start_date + timedelta(days=random_days)
        
        # توليد أرقام المشاركين
        target_participants = random.randint(20, 200)
        participants = random.randint(int(target_participants * 0.7), target_participants)
        male_participants = random.randint(0, participants)
        female_participants = participants - male_participants
        
        # توليد الميزانية والتكلفة
        budget = random.uniform(5000, 50000)
        actual_cost = random.uniform(budget * 0.8, budget * 1.1)
        
        # بيانات الاتصال
        contact_person = random.choice(persons)
        contact_phone = f"77{random.randint(1000000, 9999999)}"
        
        activity_data = {
            "person": random.choice(persons),
            "activity_name": f"{activity_name} - {i+1}",
            "parent_category": parent_category,
            "sub_category": sub_category,
            "is_approved": random.choice(approval_status),
            "governorate": governorate,
            "district": district,
            "sub_district": sub_district,
            "village": village,
            "participants": participants,
            "target_participants": target_participants,
            "male_participants": male_participants,
            "female_participants": female_participants,
            "budget": round(budget, 2),
            "actual_cost": round(actual_cost, 2),
            "results": random.choice(results),
            "recommendations": random.choice(recommendations),
            "notes": random.choice(notes),
            "activity_date": activity_date.strftime("%Y-%m-%d"),
            "start_date": (activity_date - timedelta(days=random.randint(1, 30))).strftime("%Y-%m-%d"),
            "end_date": (activity_date + timedelta(days=random.randint(1, 30))).strftime("%Y-%m-%d"),
            "status": random.choice(statuses),
            "priority": random.choice(priorities),
            "location_coordinates": f"{random.uniform(12.0, 18.0):.6f},{random.uniform(42.0, 54.0):.6f}",
            "contact_person": contact_person,
            "contact_phone": contact_phone
        }
        
        activities_data.append(activity_data)
    
    return activities_data


def insert_test_data(activities_data):
    """إدراج البيانات التجريبية في قاعدة البيانات"""
    conn = connect_database()
    cursor = conn.cursor()

    try:
        print("🚀 بدء إدراج البيانات التجريبية...")

        # إدراج البيانات
        inserted_count = 0
        for i, activity in enumerate(activities_data):
            try:
                # تحضير الاستعلام
                fields = list(activity.keys())
                placeholders = ', '.join(['?' for _ in fields])
                values = list(activity.values())

                query = f"INSERT INTO activities ({', '.join(fields)}) VALUES ({placeholders})"
                cursor.execute(query, values)

                # إضافة سجل في جدول النشاطات
                activity_id = cursor.lastrowid
                cursor.execute('''
                    INSERT INTO activity_log (activity_id, action, new_values, user_name)
                    VALUES (?, ?, ?, ?)
                ''', (activity_id, "إضافة", json.dumps(activity, ensure_ascii=False), "مولد البيانات التجريبية"))

                inserted_count += 1

                # عرض التقدم
                if (i + 1) % 50 == 0:
                    print(f"✅ تم إدراج {i + 1} نشاط...")

            except Exception as e:
                print(f"❌ خطأ في إدراج النشاط {i + 1}: {e}")
                continue

        # حفظ التغييرات
        conn.commit()
        print(f"🎉 تم إدراج {inserted_count} نشاط بنجاح!")

        # إضافة البيانات المرجعية
        add_reference_data(cursor, activities_data)
        conn.commit()

        return inserted_count

    except Exception as e:
        print(f"❌ خطأ عام في إدراج البيانات: {e}")
        conn.rollback()
        return 0
    finally:
        conn.close()


def add_reference_data(cursor, activities_data):
    """إضافة البيانات المرجعية (الأشخاص، التصنيفات، المواقع)"""
    print("📋 إضافة البيانات المرجعية...")

    # جمع البيانات الفريدة
    persons = set()
    categories = set()
    locations = set()

    for activity in activities_data:
        persons.add(activity['person'])
        categories.add((activity['parent_category'], activity['sub_category']))
        locations.add((activity['governorate'], activity['district'],
                      activity['sub_district'], activity['village']))

    # إدراج الأشخاص
    for person in persons:
        try:
            cursor.execute('''
                INSERT OR IGNORE INTO persons (name, position, organization, is_active)
                VALUES (?, ?, ?, ?)
            ''', (person, "منفذ أنشطة", "منظمة تنموية", 1))
        except:
            pass

    # إدراج التصنيفات
    for parent_cat, sub_cat in categories:
        try:
            cursor.execute('''
                INSERT OR IGNORE INTO categories (parent_category, sub_category, description, is_active)
                VALUES (?, ?, ?, ?)
            ''', (parent_cat, sub_cat, f"تصنيف {sub_cat} ضمن {parent_cat}", 1))
        except:
            pass

    # إدراج المواقع
    for gov, dist, sub_dist, village in locations:
        try:
            cursor.execute('''
                INSERT OR IGNORE INTO locations (governorate, district, sub_district, village, is_active)
                VALUES (?, ?, ?, ?, ?)
            ''', (gov, dist, sub_dist, village, 1))
        except:
            pass

    print("✅ تم إضافة البيانات المرجعية بنجاح!")


def generate_summary_report(conn):
    """توليد تقرير ملخص للبيانات المدرجة"""
    cursor = conn.cursor()

    print("\n📊 تقرير ملخص البيانات:")
    print("=" * 50)

    # إجمالي الأنشطة
    cursor.execute("SELECT COUNT(*) FROM activities")
    total_activities = cursor.fetchone()[0]
    print(f"📋 إجمالي الأنشطة: {total_activities}")

    # الأنشطة حسب الحالة
    cursor.execute("SELECT status, COUNT(*) FROM activities GROUP BY status")
    status_counts = cursor.fetchall()
    print("\n📈 الأنشطة حسب الحالة:")
    for status, count in status_counts:
        print(f"   {status}: {count}")

    # الأنشطة حسب المحافظة
    cursor.execute("SELECT governorate, COUNT(*) FROM activities GROUP BY governorate ORDER BY COUNT(*) DESC")
    gov_counts = cursor.fetchall()
    print("\n🗺️ الأنشطة حسب المحافظة:")
    for gov, count in gov_counts:
        print(f"   {gov}: {count}")

    # الأنشطة حسب التصنيف
    cursor.execute("SELECT parent_category, COUNT(*) FROM activities GROUP BY parent_category ORDER BY COUNT(*) DESC")
    cat_counts = cursor.fetchall()
    print("\n📂 الأنشطة حسب التصنيف:")
    for cat, count in cat_counts:
        print(f"   {cat}: {count}")

    # إجمالي المشاركين
    cursor.execute("SELECT SUM(participants) FROM activities")
    total_participants = cursor.fetchone()[0] or 0
    print(f"\n👥 إجمالي المشاركين: {total_participants:,}")

    # إجمالي الميزانية
    cursor.execute("SELECT SUM(budget) FROM activities")
    total_budget = cursor.fetchone()[0] or 0
    print(f"💰 إجمالي الميزانية: {total_budget:,.2f} ريال")

    print("=" * 50)


def main():
    """الدالة الرئيسية"""
    print("🏛️ مولد البيانات التجريبية - نظام إدارة الأنشطة")
    print("=" * 60)

    try:
        # التحقق من وجود قاعدة البيانات
        conn = connect_database()
        cursor = conn.cursor()

        # التحقق من وجود الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='activities'")
        if not cursor.fetchone():
            print("❌ جدول الأنشطة غير موجود. يرجى تشغيل التطبيق الرئيسي أولاً.")
            return

        # عرض العدد الحالي للأنشطة
        cursor.execute("SELECT COUNT(*) FROM activities")
        current_count = cursor.fetchone()[0]
        print(f"📊 العدد الحالي للأنشطة: {current_count}")

        conn.close()

        # تأكيد من المستخدم
        response = input("\n❓ هل تريد إضافة 500 نشاط تجريبي؟ (y/n): ")
        if response.lower() not in ['y', 'yes', 'نعم']:
            print("❌ تم إلغاء العملية.")
            return

        # توليد البيانات
        print("\n🔄 توليد البيانات التجريبية...")
        activities_data = generate_test_data()
        print(f"✅ تم توليد {len(activities_data)} نشاط تجريبي")

        # إدراج البيانات
        inserted_count = insert_test_data(activities_data)

        if inserted_count > 0:
            print(f"\n🎉 تم إدراج {inserted_count} نشاط بنجاح!")

            # توليد تقرير ملخص
            conn = connect_database()
            generate_summary_report(conn)
            conn.close()

            print("\n✅ تم الانتهاء من إضافة البيانات التجريبية!")
            print("💡 يمكنك الآن تشغيل التطبيق لرؤية البيانات الجديدة.")
        else:
            print("❌ فشل في إدراج البيانات.")

    except Exception as e:
        print(f"❌ خطأ عام: {e}")


if __name__ == "__main__":
    main()
