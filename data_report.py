#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تقرير البيانات المدرجة - عرض إحصائيات شاملة
"""

import sqlite3
from datetime import datetime

def connect_database():
    """الاتصال بقاعدة البيانات"""
    return sqlite3.connect("activities.db")

def generate_comprehensive_report():
    """توليد تقرير شامل للبيانات"""
    conn = connect_database()
    cursor = conn.cursor()
    
    print("📊 تقرير شامل لبيانات الأنشطة")
    print("=" * 60)
    print(f"📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. إجمالي الأنشطة
    cursor.execute("SELECT COUNT(*) FROM activities")
    total_activities = cursor.fetchone()[0]
    print(f"\n📋 إجمالي الأنشطة: {total_activities:,}")
    
    # 2. الأنشطة حسب الحالة
    print("\n📈 توزيع الأنشطة حسب الحالة:")
    cursor.execute("SELECT status, COUNT(*) FROM activities GROUP BY status ORDER BY COUNT(*) DESC")
    status_data = cursor.fetchall()
    for status, count in status_data:
        percentage = (count / total_activities) * 100
        print(f"   {status}: {count:,} ({percentage:.1f}%)")
    
    # 3. الأنشطة حسب الأولوية
    print("\n⭐ توزيع الأنشطة حسب الأولوية:")
    cursor.execute("SELECT priority, COUNT(*) FROM activities GROUP BY priority ORDER BY COUNT(*) DESC")
    priority_data = cursor.fetchall()
    for priority, count in priority_data:
        percentage = (count / total_activities) * 100
        print(f"   {priority}: {count:,} ({percentage:.1f}%)")
    
    # 4. الأنشطة حسب المحافظة
    print("\n🗺️ توزيع الأنشطة حسب المحافظة:")
    cursor.execute("SELECT governorate, COUNT(*) FROM activities GROUP BY governorate ORDER BY COUNT(*) DESC")
    gov_data = cursor.fetchall()
    for gov, count in gov_data:
        percentage = (count / total_activities) * 100
        print(f"   {gov}: {count:,} ({percentage:.1f}%)")
    
    # 5. الأنشطة حسب التصنيف الرئيسي
    print("\n📂 توزيع الأنشطة حسب التصنيف الرئيسي:")
    cursor.execute("SELECT parent_category, COUNT(*) FROM activities GROUP BY parent_category ORDER BY COUNT(*) DESC")
    cat_data = cursor.fetchall()
    for cat, count in cat_data:
        percentage = (count / total_activities) * 100
        print(f"   {cat}: {count:,} ({percentage:.1f}%)")
    
    # 6. الأنشطة حسب التصنيف الفرعي
    print("\n📁 أهم التصنيفات الفرعية:")
    cursor.execute("SELECT sub_category, COUNT(*) FROM activities GROUP BY sub_category ORDER BY COUNT(*) DESC LIMIT 10")
    subcat_data = cursor.fetchall()
    for subcat, count in subcat_data:
        percentage = (count / total_activities) * 100
        print(f"   {subcat}: {count:,} ({percentage:.1f}%)")
    
    # 7. إحصائيات المشاركين
    print("\n👥 إحصائيات المشاركين:")
    cursor.execute("SELECT SUM(participants), SUM(target_participants), SUM(male_participants), SUM(female_participants) FROM activities")
    participants_data = cursor.fetchone()
    actual_participants = participants_data[0] or 0
    target_participants = participants_data[1] or 0
    male_participants = participants_data[2] or 0
    female_participants = participants_data[3] or 0
    
    print(f"   إجمالي المشاركين الفعلي: {actual_participants:,}")
    print(f"   إجمالي المشاركين المستهدف: {target_participants:,}")
    print(f"   المشاركين الذكور: {male_participants:,}")
    print(f"   المشاركات الإناث: {female_participants:,}")
    
    if target_participants > 0:
        achievement_rate = (actual_participants / target_participants) * 100
        print(f"   معدل تحقيق الهدف: {achievement_rate:.1f}%")
    
    if actual_participants > 0:
        male_percentage = (male_participants / actual_participants) * 100
        female_percentage = (female_participants / actual_participants) * 100
        print(f"   نسبة الذكور: {male_percentage:.1f}%")
        print(f"   نسبة الإناث: {female_percentage:.1f}%")
    
    # 8. إحصائيات الميزانية
    print("\n💰 إحصائيات الميزانية:")
    cursor.execute("SELECT SUM(budget), SUM(actual_cost), AVG(budget), AVG(actual_cost) FROM activities")
    budget_data = cursor.fetchone()
    total_budget = budget_data[0] or 0
    total_actual_cost = budget_data[1] or 0
    avg_budget = budget_data[2] or 0
    avg_actual_cost = budget_data[3] or 0
    
    print(f"   إجمالي الميزانية المخططة: {total_budget:,.2f} ريال")
    print(f"   إجمالي التكلفة الفعلية: {total_actual_cost:,.2f} ريال")
    print(f"   متوسط الميزانية للنشاط: {avg_budget:,.2f} ريال")
    print(f"   متوسط التكلفة الفعلية للنشاط: {avg_actual_cost:,.2f} ريال")
    
    if total_budget > 0:
        budget_efficiency = (total_actual_cost / total_budget) * 100
        print(f"   كفاءة استخدام الميزانية: {budget_efficiency:.1f}%")
    
    # 9. الأنشطة حسب الشهر
    print("\n📅 توزيع الأنشطة حسب الشهر:")
    cursor.execute("""
        SELECT strftime('%Y-%m', activity_date) as month, COUNT(*) 
        FROM activities 
        GROUP BY month 
        ORDER BY month DESC 
        LIMIT 12
    """)
    monthly_data = cursor.fetchall()
    for month, count in monthly_data:
        print(f"   {month}: {count:,} نشاط")
    
    # 10. أكثر المنفذين نشاطاً
    print("\n👨‍💼 أكثر المنفذين نشاطاً:")
    cursor.execute("SELECT person, COUNT(*) FROM activities GROUP BY person ORDER BY COUNT(*) DESC LIMIT 10")
    person_data = cursor.fetchall()
    for person, count in person_data:
        percentage = (count / total_activities) * 100
        print(f"   {person}: {count:,} ({percentage:.1f}%)")
    
    # 11. الأنشطة المعتمدة
    print("\n✅ حالة الاعتماد:")
    cursor.execute("SELECT is_approved, COUNT(*) FROM activities GROUP BY is_approved")
    approval_data = cursor.fetchall()
    for approval, count in approval_data:
        percentage = (count / total_activities) * 100
        status_text = "معتمد" if approval == "نعم" else "غير معتمد"
        print(f"   {status_text}: {count:,} ({percentage:.1f}%)")
    
    # 12. إحصائيات إضافية
    print("\n📊 إحصائيات إضافية:")
    
    # أحدث نشاط
    cursor.execute("SELECT activity_name, activity_date FROM activities ORDER BY activity_date DESC LIMIT 1")
    latest_activity = cursor.fetchone()
    if latest_activity:
        print(f"   أحدث نشاط: {latest_activity[0]} ({latest_activity[1]})")
    
    # أقدم نشاط
    cursor.execute("SELECT activity_name, activity_date FROM activities ORDER BY activity_date ASC LIMIT 1")
    oldest_activity = cursor.fetchone()
    if oldest_activity:
        print(f"   أقدم نشاط: {oldest_activity[0]} ({oldest_activity[1]})")
    
    # أكبر نشاط من حيث المشاركين
    cursor.execute("SELECT activity_name, participants FROM activities ORDER BY participants DESC LIMIT 1")
    largest_activity = cursor.fetchone()
    if largest_activity:
        print(f"   أكبر نشاط (مشاركين): {largest_activity[0]} ({largest_activity[1]:,} مشارك)")
    
    # أكبر نشاط من حيث الميزانية
    cursor.execute("SELECT activity_name, budget FROM activities ORDER BY budget DESC LIMIT 1")
    most_expensive = cursor.fetchone()
    if most_expensive:
        print(f"   أكبر نشاط (ميزانية): {most_expensive[0]} ({most_expensive[1]:,.2f} ريال)")
    
    print("\n" + "=" * 60)
    print("✅ انتهى التقرير")
    
    conn.close()

def generate_summary_stats():
    """توليد إحصائيات مختصرة"""
    conn = connect_database()
    cursor = conn.cursor()
    
    print("📈 ملخص سريع للإحصائيات")
    print("-" * 30)
    
    # العدد الإجمالي
    cursor.execute("SELECT COUNT(*) FROM activities")
    total = cursor.fetchone()[0]
    print(f"📋 إجمالي الأنشطة: {total:,}")
    
    # المشاركين
    cursor.execute("SELECT SUM(participants) FROM activities")
    participants = cursor.fetchone()[0] or 0
    print(f"👥 إجمالي المشاركين: {participants:,}")
    
    # الميزانية
    cursor.execute("SELECT SUM(budget) FROM activities")
    budget = cursor.fetchone()[0] or 0
    print(f"💰 إجمالي الميزانية: {budget:,.2f} ريال")
    
    # الأنشطة المكتملة
    cursor.execute("SELECT COUNT(*) FROM activities WHERE status = 'مكتمل'")
    completed = cursor.fetchone()[0]
    completion_rate = (completed / total) * 100 if total > 0 else 0
    print(f"✅ الأنشطة المكتملة: {completed:,} ({completion_rate:.1f}%)")
    
    conn.close()

def main():
    """الدالة الرئيسية"""
    try:
        print("🏛️ نظام تقارير الأنشطة")
        print("=" * 40)
        
        choice = input("\nاختر نوع التقرير:\n1. تقرير شامل\n2. ملخص سريع\nالاختيار (1 أو 2): ")
        
        if choice == "1":
            generate_comprehensive_report()
        elif choice == "2":
            generate_summary_stats()
        else:
            print("❌ اختيار غير صحيح")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
