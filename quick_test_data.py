#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد البيانات التجريبية السريع - إضافة 500 نشاط تجريبي
"""

import sqlite3
import random
import json
from datetime import datetime, timedelta

def generate_quick_test_data():
    """توليد 500 من البيانات التجريبية بسرعة"""
    
    # البيانات الأساسية
    persons = [
        "أحمد محمد علي", "فاطمة سالم أحمد", "محمد عبدالله حسن", "سارة علي محمد",
        "خالد حسن علي", "ليلى محمد سالم", "عبدالرحمن أحمد علي", "نادية حسن محمد",
        "يوسف علي أحمد", "زينب محمد حسن", "عمر سالم علي", "رقية أحمد محمد"
    ]
    
    # التصنيفات
    categories = [
        ("التعليم والتدريب", "التدريب المهني"),
        ("التعليم والتدريب", "التعليم المستمر"),
        ("الصحة والتوعية", "التوعية الصحية"),
        ("الصحة والتوعية", "الصحة الإنجابية"),
        ("التنمية المجتمعية", "التنمية الاقتصادية"),
        ("التنمية المجتمعية", "تمكين المرأة"),
        ("البيئة والزراعة", "الزراعة المستدامة"),
        ("البيئة والزراعة", "حماية البيئة"),
        ("الثقافة والفنون", "الفنون التراثية"),
        ("الثقافة والفنون", "الأدب والشعر")
    ]
    
    # الأنشطة
    activities = [
        "ورشة تدريبية", "دورة تعليمية", "حملة توعية", "مشروع تنموي",
        "برنامج تأهيلي", "ندوة علمية", "مؤتمر", "معرض", "مهرجان", "حفل"
    ]
    
    # المواقع
    locations = [
        ("صنعاء", "الأمانة", "شعوب", "الحصبة"),
        ("صنعاء", "سنحان", "الثورة", "شملان"),
        ("عدن", "المعلا", "كريتر", "التواهي"),
        ("عدن", "الشيخ عثمان", "المنصورة", "الشعب"),
        ("تعز", "المدينة", "القاهرة", "الروضة"),
        ("تعز", "صالة", "الدمنة", "الضباب"),
        ("الحديدة", "الحديدة", "الميناء", "الخوخة"),
        ("إب", "إب", "الظهار", "الضالع")
    ]
    
    # حالات وأولويات
    statuses = ["مخطط", "جاري", "مكتمل", "ملغي"]
    priorities = ["عالي", "متوسط", "منخفض"]
    approval_status = ["نعم", "لا"]
    
    # نتائج وتوصيات
    results = [
        "تم تحقيق الأهداف المطلوبة بنجاح",
        "نجاح جزئي مع تحديات في التنفيذ",
        "تجاوز التوقعات المحددة",
        "نتائج مرضية مع إمكانية التحسين"
    ]
    
    recommendations = [
        "زيادة الدعم المالي للمشاريع المماثلة",
        "تكرار النشاط في مناطق أخرى",
        "تطوير الأدوات والوسائل المستخدمة",
        "زيادة فترة التدريب والتأهيل"
    ]
    
    notes = [
        "تم التنفيذ وفقاً للخطة المحددة",
        "تفاعل إيجابي من المجتمع المحلي",
        "تحديات لوجستية تم التغلب عليها",
        "مشاركة فعالة من الفئة المستهدفة"
    ]
    
    # توليد البيانات
    activities_data = []
    
    print("🔄 توليد البيانات التجريبية...")
    
    for i in range(500):
        # اختيار عشوائي
        parent_category, sub_category = random.choice(categories)
        activity_name = f"{random.choice(activities)} {random.choice(['في', 'حول', 'عن'])} {sub_category} - {i+1}"
        governorate, district, sub_district, village = random.choice(locations)
        
        # توليد تاريخ عشوائي
        start_date = datetime.now() - timedelta(days=365)
        end_date = datetime.now() + timedelta(days=180)
        random_days = random.randint(0, (end_date - start_date).days)
        activity_date = start_date + timedelta(days=random_days)
        
        # أرقام المشاركين
        target_participants = random.randint(20, 150)
        participants = random.randint(int(target_participants * 0.7), target_participants)
        male_participants = random.randint(0, participants)
        female_participants = participants - male_participants
        
        # الميزانية
        budget = random.uniform(5000, 30000)
        actual_cost = random.uniform(budget * 0.8, budget * 1.1)
        
        activity_data = {
            "person": random.choice(persons),
            "activity_name": activity_name,
            "parent_category": parent_category,
            "sub_category": sub_category,
            "is_approved": random.choice(approval_status),
            "governorate": governorate,
            "district": district,
            "sub_district": sub_district,
            "village": village,
            "participants": participants,
            "target_participants": target_participants,
            "male_participants": male_participants,
            "female_participants": female_participants,
            "budget": round(budget, 2),
            "actual_cost": round(actual_cost, 2),
            "results": random.choice(results),
            "recommendations": random.choice(recommendations),
            "notes": random.choice(notes),
            "activity_date": activity_date.strftime("%Y-%m-%d"),
            "start_date": (activity_date - timedelta(days=random.randint(1, 15))).strftime("%Y-%m-%d"),
            "end_date": (activity_date + timedelta(days=random.randint(1, 15))).strftime("%Y-%m-%d"),
            "status": random.choice(statuses),
            "priority": random.choice(priorities),
            "location_coordinates": f"{random.uniform(12.0, 18.0):.6f},{random.uniform(42.0, 54.0):.6f}",
            "contact_person": random.choice(persons),
            "contact_phone": f"77{random.randint(1000000, 9999999)}"
        }
        
        activities_data.append(activity_data)
        
        # عرض التقدم
        if (i + 1) % 100 == 0:
            print(f"✅ تم توليد {i + 1} نشاط...")
    
    return activities_data

def insert_data_to_database(activities_data):
    """إدراج البيانات في قاعدة البيانات"""
    try:
        conn = sqlite3.connect("activities.db")
        cursor = conn.cursor()
        
        print("🚀 بدء إدراج البيانات...")
        
        inserted_count = 0
        for i, activity in enumerate(activities_data):
            try:
                # تحضير الاستعلام
                fields = list(activity.keys())
                placeholders = ', '.join(['?' for _ in fields])
                values = list(activity.values())
                
                query = f"INSERT INTO activities ({', '.join(fields)}) VALUES ({placeholders})"
                cursor.execute(query, values)
                
                inserted_count += 1
                
                # عرض التقدم
                if (i + 1) % 100 == 0:
                    print(f"✅ تم إدراج {i + 1} نشاط...")
                    
            except Exception as e:
                print(f"❌ خطأ في إدراج النشاط {i + 1}: {e}")
                continue
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        return inserted_count
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return 0

def main():
    """الدالة الرئيسية"""
    print("🏛️ مولد البيانات التجريبية السريع")
    print("=" * 50)
    
    try:
        # توليد البيانات
        activities_data = generate_quick_test_data()
        print(f"✅ تم توليد {len(activities_data)} نشاط تجريبي")
        
        # إدراج البيانات
        inserted_count = insert_data_to_database(activities_data)
        
        if inserted_count > 0:
            print(f"\n🎉 تم إدراج {inserted_count} نشاط بنجاح!")
            print("💡 يمكنك الآن تشغيل التطبيق لرؤية البيانات الجديدة.")
        else:
            print("❌ فشل في إدراج البيانات.")
            
    except Exception as e:
        print(f"❌ خطأ عام: {e}")

if __name__ == "__main__":
    main()
